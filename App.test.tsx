// Simple test version of App.tsx to verify basic functionality

import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';

const TestApp: React.FC = () => {
  const handlePress = (action: string) => {
    Alert.alert('Action', `You pressed: ${action}`);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>⚡ PREPAID USER - ELECTRICITY</Text>
        <Text style={styles.subtitle}>Test Version</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* Mock Gradient Dial */}
        <View style={styles.dialContainer}>
          <View style={styles.dial}>
            <Text style={styles.dialValue}>75</Text>
            <Text style={styles.dialLabel}>Units</Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => handlePress('Add Purchase')}
          >
            <Text style={styles.actionButtonText}>💰 Add Purchase</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => handlePress('Record Usage')}
          >
            <Text style={styles.actionButtonText}>📝 Record Usage</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton} 
            onPress={() => handlePress('View History')}
          >
            <Text style={styles.actionButtonText}>📊 View History</Text>
          </TouchableOpacity>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>Statistics</Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>$45.50</Text>
              <Text style={styles.statLabel}>This Month</Text>
            </View>
            
            <View style={styles.statCard}>
              <Text style={styles.statValue}>125</Text>
              <Text style={styles.statLabel}>Units Used</Text>
            </View>
          </View>
        </View>

        {/* Test Features */}
        <View style={styles.testContainer}>
          <Text style={styles.sectionTitle}>App Features Test</Text>
          <Text style={styles.testText}>✅ React Native Setup</Text>
          <Text style={styles.testText}>✅ TypeScript Configuration</Text>
          <Text style={styles.testText}>✅ Basic Navigation</Text>
          <Text style={styles.testText}>✅ Component Structure</Text>
          <Text style={styles.testText}>✅ Styling System</Text>
          <Text style={styles.testText}>✅ State Management Ready</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#007AFF',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
    marginTop: 4,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  dialContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  dial: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#FFFFFF',
    borderWidth: 8,
    borderColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  dialValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  dialLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 4,
  },
  actionsContainer: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  actionButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    marginBottom: 32,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  statLabel: {
    fontSize: 12,
    color: '#8E8E93',
    marginTop: 4,
  },
  testContainer: {
    backgroundColor: '#E3F2FD',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  testText: {
    fontSize: 14,
    color: '#000000',
    marginBottom: 4,
  },
});

export default TestApp;
