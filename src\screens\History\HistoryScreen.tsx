// History Screen - View purchase and usage history

import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, FlatList } from 'react-native';
import { useAppSelector } from '@/store';
import { selectPurchases, selectPurchasesTotals } from '@/store/slices/purchasesSlice';
import { selectUsage } from '@/store/slices/usageSlice';
import { selectUserCurrency, selectUserUnitType } from '@/store/slices/userSlice';
import Container from '@/components/common/Container';
import InfoCard from '@/components/common/InfoCard';
import { formatCurrency, formatUnits, formatDate } from '@/utils/formatters';
import { PurchaseEntry, UsageEntry } from '@/types';

type HistoryEntry = (PurchaseEntry | UsageEntry) & { type: 'purchase' | 'usage' };

const HistoryScreen: React.FC = () => {
  const purchases = useAppSelector(selectPurchases);
  const usage = useAppSelector((state) => state.usage.entries);
  const purchaseTotals = useAppSelector(selectPurchasesTotals);
  const usageTotals = useAppSelector((state) => state.usage.totals);
  const currency = useAppSelector(selectUserCurrency) || 'USD';
  const unitType = useAppSelector(selectUserUnitType) || 'Units';

  const [activeTab, setActiveTab] = useState<'all' | 'purchases' | 'usage'>('all');
  const [viewMode, setViewMode] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  // Combine and sort all entries
  const allEntries: HistoryEntry[] = [
    ...purchases.map(p => ({ ...p, type: 'purchase' as const })),
    ...usage.map(u => ({ ...u, type: 'usage' as const })),
  ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // Filter entries based on active tab
  const filteredEntries = allEntries.filter(entry => {
    if (activeTab === 'all') return true;
    if (activeTab === 'purchases') return entry.type === 'purchase';
    if (activeTab === 'usage') return entry.type === 'usage';
    return true;
  });

  const renderHistoryItem = ({ item }: { item: HistoryEntry }) => {
    const isPurchase = item.type === 'purchase';
    const purchaseItem = item as PurchaseEntry;
    const usageItem = item as UsageEntry;

    return (
      <View style={styles.historyItem}>
        <View style={styles.historyHeader}>
          <View style={[
            styles.typeIndicator,
            { backgroundColor: isPurchase ? '#34C759' : '#FF9500' }
          ]}>
            <Text style={styles.typeText}>
              {isPurchase ? 'P' : 'U'}
            </Text>
          </View>
          <View style={styles.historyContent}>
            <Text style={styles.historyTitle}>
              {isPurchase ? 'Purchase' : 'Usage Recording'}
            </Text>
            <Text style={styles.historyDate}>
              {formatDate(item.date, 'datetime')}
            </Text>
          </View>
          <View style={styles.historyValue}>
            {isPurchase ? (
              <>
                <Text style={styles.valueText}>
                  {formatCurrency(purchaseItem.currencyAmount, currency)}
                </Text>
                <Text style={styles.subValueText}>
                  {formatUnits(purchaseItem.unitsPurchased, unitType)}
                </Text>
              </>
            ) : (
              <>
                <Text style={styles.valueText}>
                  {formatUnits(usageItem.usageAmount, unitType)}
                </Text>
                <Text style={styles.subValueText}>
                  Used
                </Text>
              </>
            )}
          </View>
        </View>
        {item.notes && (
          <Text style={styles.historyNotes}>{item.notes}</Text>
        )}
      </View>
    );
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container}>
        {/* Totals Section */}
        <View style={styles.totalsSection}>
          <Text style={styles.sectionTitle}>Summary</Text>
          <View style={styles.totalsGrid}>
            <InfoCard
              title="Weekly Purchases"
              value={formatCurrency(purchaseTotals.weekly, currency)}
              icon="calendar"
              color="#34C759"
            />
            <InfoCard
              title="Monthly Purchases"
              value={formatCurrency(purchaseTotals.monthly, currency)}
              icon="calendar"
              color="#AF52DE"
            />
            <InfoCard
              title="Weekly Usage"
              value={formatUnits(usageTotals.weekly, unitType)}
              icon="trending-down"
              color="#FF9500"
            />
            <InfoCard
              title="Monthly Usage"
              value={formatUnits(usageTotals.monthly, unitType)}
              icon="trending-down"
              color="#FF3B30"
            />
          </View>
        </View>

        {/* View Mode Selector */}
        <View style={styles.viewModeSection}>
          <Text style={styles.viewModeTitle}>View Mode</Text>
          <View style={styles.viewModeButtons}>
            {['daily', 'weekly', 'monthly'].map((mode) => (
              <TouchableOpacity
                key={mode}
                style={[
                  styles.viewModeButton,
                  viewMode === mode && styles.activeViewModeButton
                ]}
                onPress={() => setViewMode(mode as 'daily' | 'weekly' | 'monthly')}
              >
                <Text style={[
                  styles.viewModeButtonText,
                  viewMode === mode && styles.activeViewModeButtonText
                ]}>
                  {mode.charAt(0).toUpperCase() + mode.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Filter Tabs */}
        <View style={styles.tabsContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'all' && styles.activeTab]}
            onPress={() => setActiveTab('all')}
          >
            <Text style={[styles.tabText, activeTab === 'all' && styles.activeTabText]}>
              All
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'purchases' && styles.activeTab]}
            onPress={() => setActiveTab('purchases')}
          >
            <Text style={[styles.tabText, activeTab === 'purchases' && styles.activeTabText]}>
              Purchases
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'usage' && styles.activeTab]}
            onPress={() => setActiveTab('usage')}
          >
            <Text style={[styles.tabText, activeTab === 'usage' && styles.activeTabText]}>
              Usage
            </Text>
          </TouchableOpacity>
        </View>

        {/* History List */}
        <View style={styles.historySection}>
          <Text style={styles.sectionTitle}>History</Text>
          {filteredEntries.length > 0 ? (
            <FlatList
              data={filteredEntries}
              renderItem={renderHistoryItem}
              keyExtractor={(item) => `${item.type}-${item.id}`}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>
                No {activeTab === 'all' ? 'entries' : activeTab} found
              </Text>
              <Text style={styles.emptyStateSubtext}>
                Start by adding a purchase or recording usage
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  totalsSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  totalsGrid: {
    gap: 12,
  },
  viewModeSection: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  viewModeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  viewModeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  viewModeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
  },
  activeViewModeButton: {
    backgroundColor: '#34C759',
  },
  viewModeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#8E8E93',
  },
  activeViewModeButtonText: {
    color: '#FFFFFF',
  },
  tabsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: 4,
    backgroundColor: '#F2F2F7',
  },
  activeTab: {
    backgroundColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8E8E93',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  historySection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  historyItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  typeText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  historyContent: {
    flex: 1,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  historyDate: {
    fontSize: 14,
    color: '#8E8E93',
  },
  historyValue: {
    alignItems: 'flex-end',
  },
  valueText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  subValueText: {
    fontSize: 12,
    color: '#8E8E93',
  },
  historyNotes: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 8,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E8E93',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
  },
});

export default HistoryScreen;
