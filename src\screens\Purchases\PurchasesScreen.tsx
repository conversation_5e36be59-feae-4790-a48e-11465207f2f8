// Purchases Screen - Add electricity purchases

import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, StyleSheet } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/store';
import { addPurchase } from '@/store/slices/purchasesSlice';
import { selectUserCurrency, selectUserCostPerUnit } from '@/store/slices/userSlice';
import Container from '@/components/common/Container';
import { ElectricityCalculator } from '@/utils/calculations';
import { formatCurrency, formatUnits } from '@/utils/formatters';

const PurchasesScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const currency = useAppSelector(selectUserCurrency) || 'USD';
  const defaultCostPerUnit = useAppSelector(selectUserCostPerUnit) || 0.15;

  const [currencyAmount, setCurrencyAmount] = useState('');
  const [unitsPurchased, setUnitsPurchased] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Calculate live preview
  const currencyValue = parseFloat(currencyAmount) || 0;
  const unitsValue = parseFloat(unitsPurchased) || 0;
  const costPerUnit = unitsValue > 0 ? currencyValue / unitsValue : 0;
  const estimatedUnits = currencyValue > 0 && defaultCostPerUnit > 0 ? currencyValue / defaultCostPerUnit : 0;

  const handleSubmit = async () => {
    if (!currencyAmount || !unitsPurchased) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (currencyValue <= 0 || unitsValue <= 0) {
      Alert.alert('Error', 'Please enter valid positive numbers');
      return;
    }

    setIsLoading(true);

    try {
      await dispatch(addPurchase({
        currencyAmount: currencyValue,
        unitsPurchased: unitsValue,
        currency,
        notes: notes.trim() || undefined,
      })).unwrap();

      // Reset form
      setCurrencyAmount('');
      setUnitsPurchased('');
      setNotes('');

      Alert.alert('Success', 'Purchase added successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to add purchase. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Add Purchase</Text>
        <Text style={styles.subtitle}>Record your electricity purchase details</Text>

        {/* Currency Amount Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Currency Amount ({currency})</Text>
          <TextInput
            style={styles.input}
            value={currencyAmount}
            onChangeText={setCurrencyAmount}
            placeholder="0.00"
            keyboardType="numeric"
            returnKeyType="next"
          />
        </View>

        {/* Units Purchased Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Units Purchased</Text>
          <TextInput
            style={styles.input}
            value={unitsPurchased}
            onChangeText={setUnitsPurchased}
            placeholder="0.00"
            keyboardType="numeric"
            returnKeyType="next"
          />
        </View>

        {/* Live Preview */}
        {(currencyValue > 0 || unitsValue > 0) && (
          <View style={styles.previewContainer}>
            <Text style={styles.previewTitle}>💡 Live Preview</Text>

            {currencyValue > 0 && !unitsValue && (
              <View style={styles.previewSection}>
                <Text style={styles.previewLabel}>Estimated Units (at {formatCurrency(defaultCostPerUnit, currency)}/unit):</Text>
                <Text style={styles.previewValue}>{formatUnits(estimatedUnits, 'Units')}</Text>
              </View>
            )}

            {currencyValue > 0 && unitsValue > 0 && (
              <>
                <View style={styles.previewSection}>
                  <Text style={styles.previewLabel}>Cost per Unit:</Text>
                  <Text style={styles.previewValue}>{formatCurrency(costPerUnit, currency, 4)}</Text>
                </View>

                <View style={styles.previewSection}>
                  <Text style={styles.previewLabel}>Total Purchase:</Text>
                  <Text style={styles.previewValue}>
                    {formatCurrency(currencyValue, currency)} → {formatUnits(unitsValue, 'Units')}
                  </Text>
                </View>

                <View style={styles.previewSection}>
                  <Text style={styles.previewLabel}>Rate Comparison:</Text>
                  <Text style={[
                    styles.previewValue,
                    { color: costPerUnit <= defaultCostPerUnit ? '#34C759' : '#FF9500' }
                  ]}>
                    {costPerUnit <= defaultCostPerUnit ? '✓ Good rate' : '⚠️ Higher than usual'}
                  </Text>
                </View>
              </>
            )}
          </View>
        )}

        {/* Notes Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Notes (Optional)</Text>
          <TextInput
            style={[styles.input, styles.notesInput]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Add any notes about this purchase..."
            multiline
            numberOfLines={3}
            returnKeyType="done"
          />
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Adding Purchase...' : 'Add Purchase'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  previewContainer: {
    backgroundColor: '#E3F2FD',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 12,
  },
  previewSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  previewLabel: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '500',
    flex: 1,
  },
  previewValue: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '600',
    textAlign: 'right',
  },
  submitButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#C6C6C8',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default PurchasesScreen;
