// General Settings Screen - Currency, Units, Cost per Unit, Threshold settings

import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet, Alert, Switch, TextInput } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectCurrentUser, updateUserSettings } from '@/store/slices/userSlice';
import { selectNotifications, updateNotifications } from '@/store/slices/settingsSlice';
import Container from '@/components/common/Container';
import QuickActionButton from '@/components/common/QuickActionButton';
import { CURRENCIES, UNIT_TYPES } from '@/constants';

const GeneralSettingsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectCurrentUser);
  const notifications = useAppSelector(selectNotifications);

  const [selectedCurrency, setSelectedCurrency] = useState(user?.currency || 'USD');
  const [selectedUnitType, setSelectedUnitType] = useState(user?.unitType || 'Units');
  const [customCurrency, setCustomCurrency] = useState(user?.customCurrencyName || '');
  const [customUnit, setCustomUnit] = useState(user?.customUnitName || '');
  const [costPerUnit, setCostPerUnit] = useState(user?.costPerUnit?.toString() || '0.15');
  const [thresholdLimit, setThresholdLimit] = useState(user?.thresholdLimit?.toString() || '10');
  const [notificationsEnabled, setNotificationsEnabled] = useState(notifications?.enabled || false);
  const [notificationTime, setNotificationTime] = useState(notifications?.time || '18:00');

  const handleSaveSettings = async () => {
    try {
      // Validate inputs
      const costValue = parseFloat(costPerUnit);
      const thresholdValue = parseFloat(thresholdLimit);

      if (isNaN(costValue) || costValue <= 0) {
        Alert.alert('Error', 'Please enter a valid cost per unit');
        return;
      }

      if (isNaN(thresholdValue) || thresholdValue <= 0) {
        Alert.alert('Error', 'Please enter a valid threshold limit');
        return;
      }

      // Update user settings
      await dispatch(updateUserSettings({
        currency: selectedCurrency,
        customCurrencyName: selectedCurrency === 'Custom' ? customCurrency : undefined,
        unitType: selectedUnitType,
        customUnitName: selectedUnitType === 'Custom' ? customUnit : undefined,
        costPerUnit: costValue,
        thresholdLimit: thresholdValue,
      })).unwrap();

      // Update notification settings
      await dispatch(updateNotifications({
        enabled: notificationsEnabled,
        time: notificationTime,
      })).unwrap();

      Alert.alert('Success', 'Settings saved successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    }
  };

  const renderCurrencyOptions = () => (
    <View style={styles.optionsContainer}>
      <Text style={styles.optionTitle}>Currency</Text>
      {CURRENCIES.map((currency) => (
        <QuickActionButton
          key={currency.value}
          title={`${currency.label} (${currency.symbol})`}
          icon="dollar"
          onPress={() => setSelectedCurrency(currency.value)}
          variant={selectedCurrency === currency.value ? 'primary' : 'secondary'}
          style={styles.optionButton}
        />
      ))}
      <QuickActionButton
        title="Custom Currency"
        icon="edit"
        onPress={() => setSelectedCurrency('Custom')}
        variant={selectedCurrency === 'Custom' ? 'primary' : 'secondary'}
        style={styles.optionButton}
      />
      {selectedCurrency === 'Custom' && (
        <View style={styles.customInput}>
          <Text style={styles.inputLabel}>Custom Currency Name</Text>
          <TextInput
            style={styles.textInput}
            value={customCurrency}
            onChangeText={setCustomCurrency}
            placeholder="Enter currency name"
          />
        </View>
      )}
    </View>
  );

  const renderUnitOptions = () => (
    <View style={styles.optionsContainer}>
      <Text style={styles.optionTitle}>Unit Type</Text>
      {UNIT_TYPES.map((unit) => (
        <QuickActionButton
          key={unit.value}
          title={unit.label}
          icon="zap"
          onPress={() => setSelectedUnitType(unit.value)}
          variant={selectedUnitType === unit.value ? 'primary' : 'secondary'}
          style={styles.optionButton}
        />
      ))}
      {selectedUnitType === 'Custom' && (
        <View style={styles.customInput}>
          <Text style={styles.inputLabel}>Custom Unit Name</Text>
          <TextInput
            style={styles.textInput}
            value={customUnit}
            onChangeText={setCustomUnit}
            placeholder="Enter unit name"
          />
        </View>
      )}
    </View>
  );

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>General Settings</Text>

        {renderCurrencyOptions()}
        {renderUnitOptions()}

        {/* Cost Per Unit */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>Cost Per Unit</Text>
          <TextInput
            style={styles.textInput}
            value={costPerUnit}
            onChangeText={setCostPerUnit}
            placeholder="0.15"
            keyboardType="decimal-pad"
          />
        </View>

        {/* Threshold Limit */}
        <View style={styles.inputSection}>
          <Text style={styles.inputLabel}>Low Units Threshold</Text>
          <TextInput
            style={styles.textInput}
            value={thresholdLimit}
            onChangeText={setThresholdLimit}
            placeholder="10"
            keyboardType="numeric"
          />
        </View>

        {/* Notifications */}
        <View style={styles.notificationSection}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <View style={styles.switchContainer}>
            <Text style={styles.switchLabel}>Enable Daily Reminders</Text>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: '#C6C6C8', true: '#007AFF' }}
              thumbColor="#FFFFFF"
            />
          </View>

          {notificationsEnabled && (
            <View style={styles.inputSection}>
              <Text style={styles.inputLabel}>Reminder Time</Text>
              <TextInput
                style={styles.textInput}
                value={notificationTime}
                onChangeText={setNotificationTime}
                placeholder="18:00"
              />
            </View>
          )}
        </View>

        {/* Save Button */}
        <QuickActionButton
          title="Save Settings"
          icon="check"
          onPress={handleSaveSettings}
          variant="primary"
          gradient={true}
          style={styles.saveButton}
        />
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 24,
    textAlign: 'center',
  },
  optionsContainer: {
    marginBottom: 24,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  optionButton: {
    marginBottom: 8,
  },
  customInput: {
    marginTop: 12,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  notificationSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
    marginBottom: 16,
  },
  switchLabel: {
    fontSize: 16,
    color: '#000000',
  },
  saveButton: {
    marginTop: 20,
    marginBottom: 40,
  },
});

export default GeneralSettingsScreen;
