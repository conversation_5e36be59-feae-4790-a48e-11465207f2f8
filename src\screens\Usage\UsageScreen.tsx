// Enhanced Usage Screen - Record electricity usage with colorful charts

import React, { useState, useMemo } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, StyleSheet } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/store';
import { addUsage, selectUsage } from '@/store/slices/usageSlice';
import { selectCurrentUnits } from '@/store';
import { selectUserUnitType, selectUserCurrency } from '@/store/slices/userSlice';
import Container from '@/components/common/Container';
import SimpleLineChart from '@/components/charts/SimpleLineChart';
import SimpleBarChart from '@/components/charts/SimpleBarChart';
import { ElectricityCalculator } from '@/utils/calculations';
import { formatUnits, formatCurrency } from '@/utils/formatters';
import { format, subDays, startOfDay } from 'date-fns';

const UsageScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const currentUnits = useAppSelector(selectCurrentUnits);
  const usageEntries = useAppSelector(selectUsage);
  const unitType = useAppSelector(selectUserUnitType) || 'Units';
  const currency = useAppSelector(selectUserCurrency) || 'USD';

  const [previousUnits, setPreviousUnits] = useState(currentUnits.toString());
  const [newCurrentUnits, setNewCurrentUnits] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Calculate usage amount
  const previousValue = parseFloat(previousUnits) || 0;
  const currentValue = parseFloat(newCurrentUnits) || 0;
  const usageAmount = currentValue > 0 ? ElectricityCalculator.calculateUsage(previousValue, currentValue) : 0;

  // Prepare chart data for the last 7 days
  const chartData = useMemo(() => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = subDays(new Date(), 6 - i);
      const dayEntries = usageEntries.filter(entry =>
        format(new Date(entry.date), 'yyyy-MM-dd') === format(date, 'yyyy-MM-dd')
      );

      const totalUsage = dayEntries.reduce((sum, entry) => sum + entry.usageAmount, 0);

      return {
        x: format(date, 'MMM dd'),
        y: totalUsage,
        label: `${totalUsage.toFixed(1)} ${unitType}`,
      };
    });

    return last7Days;
  }, [usageEntries, unitType]);

  // Weekly totals for bar chart
  const weeklyData = useMemo(() => {
    const weeks = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
    return weeks.map((week, index) => ({
      x: week,
      y: Math.random() * 50 + 20, // Placeholder data - replace with actual weekly calculations
      label: `${(Math.random() * 50 + 20).toFixed(1)} ${unitType}`,
    }));
  }, [unitType]);

  const handleSubmit = async () => {
    if (!newCurrentUnits) {
      Alert.alert('Error', 'Please enter the current unit reading');
      return;
    }

    if (currentValue < 0) {
      Alert.alert('Error', 'Please enter a valid positive number');
      return;
    }

    setIsLoading(true);

    try {
      await dispatch(addUsage({
        previousUnits: previousValue,
        currentUnits: currentValue,
        notes: notes.trim() || undefined,
      })).unwrap();

      // Reset form
      setPreviousUnits(currentValue.toString());
      setNewCurrentUnits('');
      setNotes('');

      Alert.alert('Success', 'Usage recorded successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to record usage. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Record Usage</Text>
        <Text style={styles.subtitle}>Enter your current meter reading and view usage trends</Text>

        {/* Usage Charts Section */}
        <View style={styles.chartsSection}>
          <Text style={styles.chartTitle}>📊 Daily Usage Trend (Last 7 Days)</Text>
          <SimpleLineChart
            data={chartData}
            title=""
            color="#FF9500"
            showPoints={true}
            showArea={true}
            animate={true}
          />

          <Text style={styles.chartTitle}>📈 Weekly Usage Comparison</Text>
          <SimpleLineChart
            data={weeklyData}
            title=""
            color="#34C759"
            showPoints={false}
            animate={true}
          />
        </View>

        {/* Current Units Display */}
        <View style={styles.currentUnitsContainer}>
          <Text style={styles.currentUnitsLabel}>Last Recorded Reading</Text>
          <Text style={styles.currentUnitsValue}>
            {formatUnits(currentUnits, unitType)}
          </Text>
        </View>

        {/* Previous Units Input (Pre-filled) */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Previous Reading ({unitType})</Text>
          <TextInput
            style={styles.input}
            value={previousUnits}
            onChangeText={setPreviousUnits}
            placeholder="0.00"
            keyboardType="numeric"
            returnKeyType="next"
          />
        </View>

        {/* Current Units Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Current Reading ({unitType})</Text>
          <TextInput
            style={styles.input}
            value={newCurrentUnits}
            onChangeText={setNewCurrentUnits}
            placeholder="0.00"
            keyboardType="numeric"
            returnKeyType="next"
          />
        </View>

        {/* Usage Calculation Display */}
        {usageAmount > 0 && (
          <View style={styles.calculationContainer}>
            <Text style={styles.calculationTitle}>⚡ Usage Calculation</Text>
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>Previous Reading:</Text>
              <Text style={styles.calculationValue}>{formatUnits(previousValue, unitType)}</Text>
            </View>
            <View style={styles.calculationRow}>
              <Text style={styles.calculationLabel}>Current Reading:</Text>
              <Text style={styles.calculationValue}>{formatUnits(currentValue, unitType)}</Text>
            </View>
            <View style={[styles.calculationRow, styles.totalRow]}>
              <Text style={styles.calculationLabel}>Usage Amount:</Text>
              <Text style={styles.totalValue}>{formatUnits(usageAmount, unitType)}</Text>
            </View>
            {usageAmount > 0 && (
              <View style={styles.calculationRow}>
                <Text style={styles.calculationLabel}>Estimated Cost:</Text>
                <Text style={styles.calculationValue}>
                  {formatCurrency(usageAmount * 0.15, currency)} {/* Using default rate */}
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Notes Input */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Notes (Optional)</Text>
          <TextInput
            style={[styles.input, styles.notesInput]}
            value={notes}
            onChangeText={setNotes}
            placeholder="Add any notes about this reading..."
            multiline
            numberOfLines={3}
            returnKeyType="done"
          />
        </View>

        {/* Submit Button */}
        <TouchableOpacity
          style={[styles.submitButton, isLoading && styles.disabledButton]}
          onPress={handleSubmit}
          disabled={isLoading}
        >
          <Text style={styles.submitButtonText}>
            {isLoading ? 'Recording Usage...' : 'Record Usage'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 24,
  },
  chartsSection: {
    marginBottom: 24,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
    marginTop: 16,
  },
  currentUnitsContainer: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    marginBottom: 24,
  },
  currentUnitsLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  currentUnitsValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  notesInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  calculationContainer: {
    backgroundColor: '#E3F2FD',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  calculationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 12,
  },
  calculationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#007AFF',
    paddingTop: 8,
    marginTop: 8,
  },
  calculationLabel: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '500',
  },
  calculationValue: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '600',
  },
  totalValue: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    backgroundColor: '#C6C6C8',
  },
  submitButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});

export default UsageScreen;
