// Enhanced Settings Screen - Main settings navigation

import React from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useAppSelector } from '@/store';
import { selectCurrentUser } from '@/store/slices/userSlice';
import { selectTheme } from '@/store/slices/settingsSlice';
import Container from '@/components/common/Container';
import QuickActionButton from '@/components/common/QuickActionButton';
import AppLogo from '@/components/common/AppLogo';
import { SCREEN_NAMES, THEMES } from '@/constants';

const SettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const user = useAppSelector(selectCurrentUser);
  const currentTheme = useAppSelector(selectTheme);

  const handleNavigateToGeneral = () => {
    navigation.navigate(SCREEN_NAMES.GENERAL_SETTINGS);
  };

  const handleNavigateToAppearance = () => {
    navigation.navigate(SCREEN_NAMES.APPEARANCE_SETTINGS);
  };

  const handleNavigateToReset = () => {
    navigation.navigate(SCREEN_NAMES.RESET_OPTIONS);
  };

  const handleDataReset = () => {
    Alert.alert(
      'Reset Dashboard Data',
      'This will clear all purchase and usage data but keep your settings. This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Reset Data',
          style: 'destructive',
          onPress: () => {
            // Implement data reset logic
            Alert.alert('Success', 'Dashboard data has been reset');
          },
        },
      ]
    );
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        {/* Header with Logo */}
        <View style={styles.header}>
          <AppLogo size={80} showText={true} variant="full" />
          <Text style={styles.title}>Settings</Text>
          <Text style={styles.subtitle}>Configure your app preferences</Text>
        </View>

        {/* User Info Summary */}
        <View style={styles.userSummary}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Currency</Text>
            <Text style={styles.summaryValue}>{user?.currency || 'USD'}</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Unit Type</Text>
            <Text style={styles.summaryValue}>{user?.unitType || 'Units'}</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Theme</Text>
            <Text style={styles.summaryValue}>{currentTheme?.name || 'Electric Blue'}</Text>
          </View>
        </View>

        {/* Settings Navigation Buttons */}
        <View style={styles.settingsButtons}>
          <Text style={styles.sectionTitle}>Settings Categories</Text>

          <QuickActionButton
            title="General Settings"
            icon="settings"
            onPress={handleNavigateToGeneral}
            variant="narrow"
            style={styles.settingButton}
          />

          <QuickActionButton
            title="Appearance"
            icon="palette"
            onPress={handleNavigateToAppearance}
            variant="narrow"
            style={styles.settingButton}
          />

          <QuickActionButton
            title="Reset Options"
            icon="refresh"
            onPress={handleNavigateToReset}
            variant="narrow"
            style={styles.settingButton}
          />
        </View>

        {/* App Information */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoTitle}>App Information</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Version</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Build</Text>
            <Text style={styles.infoValue}>2024.01.001</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Developer</Text>
            <Text style={styles.infoValue}>Pathfinders2c</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>User ID</Text>
            <Text style={styles.infoValue}>{user?.id?.slice(0, 8) || 'Not set'}...</Text>
          </View>
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
  },
  userSummary: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  summaryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '600',
  },
  summaryValue: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '600',
  },
  settingsButtons: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  settingButton: {
    marginBottom: 12,
  },
  appInfo: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    marginBottom: 32,
  },
  appInfoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  infoLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  infoValue: {
    fontSize: 14,
    color: '#000000',
    fontWeight: '500',
  },
});

export default SettingsScreen;
