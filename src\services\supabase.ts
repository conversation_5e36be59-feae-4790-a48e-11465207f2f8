// Supabase Client Configuration and Services
import { createClient } from '@supabase/supabase-js';
import { Purchase, UsageRecord, UserSettings } from '@/types';

// Supabase configuration
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database table names
const TABLES = {
  USERS: 'users',
  PURCHASES: 'purchases',
  USAGE: 'usage_records',
  SETTINGS: 'user_settings',
} as const;

// User Services
export const userService = {
  async getUser(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) throw error;
    return data;
  },

  async createUser(userData: Partial<UserSettings>) {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert([userData])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateUser(userId: string, updates: Partial<UserSettings>) {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update(updates)
      .eq('id', userId)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },
};

// Purchase Services
export const purchaseService = {
  async getPurchases(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.PURCHASES)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data as Purchase[];
  },

  async addPurchase(purchase: Omit<Purchase, 'id' | 'createdAt'>) {
    const { data, error } = await supabase
      .from(TABLES.PURCHASES)
      .insert([{
        ...purchase,
        created_at: new Date().toISOString(),
      }])
      .select()
      .single();
    
    if (error) throw error;
    return data as Purchase;
  },

  async updatePurchase(purchaseId: string, updates: Partial<Purchase>) {
    const { data, error } = await supabase
      .from(TABLES.PURCHASES)
      .update(updates)
      .eq('id', purchaseId)
      .select()
      .single();
    
    if (error) throw error;
    return data as Purchase;
  },

  async deletePurchase(purchaseId: string) {
    const { error } = await supabase
      .from(TABLES.PURCHASES)
      .delete()
      .eq('id', purchaseId);
    
    if (error) throw error;
  },
};

// Usage Services
export const usageService = {
  async getUsageRecords(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.USAGE)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data as UsageRecord[];
  },

  async addUsageRecord(usage: Omit<UsageRecord, 'id' | 'createdAt'>) {
    const { data, error } = await supabase
      .from(TABLES.USAGE)
      .insert([{
        ...usage,
        created_at: new Date().toISOString(),
      }])
      .select()
      .single();
    
    if (error) throw error;
    return data as UsageRecord;
  },

  async updateUsageRecord(usageId: string, updates: Partial<UsageRecord>) {
    const { data, error } = await supabase
      .from(TABLES.USAGE)
      .update(updates)
      .eq('id', usageId)
      .select()
      .single();
    
    if (error) throw error;
    return data as UsageRecord;
  },

  async deleteUsageRecord(usageId: string) {
    const { error } = await supabase
      .from(TABLES.USAGE)
      .delete()
      .eq('id', usageId);
    
    if (error) throw error;
  },
};

// Settings Services
export const settingsService = {
  async getUserSettings(userId: string) {
    const { data, error } = await supabase
      .from(TABLES.SETTINGS)
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateUserSettings(userId: string, settings: Partial<UserSettings>) {
    const { data, error } = await supabase
      .from(TABLES.SETTINGS)
      .upsert([{
        user_id: userId,
        ...settings,
        updated_at: new Date().toISOString(),
      }])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },
};

// Real-time subscriptions
export const subscriptions = {
  subscribeToPurchases(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('purchases')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: TABLES.PURCHASES,
          filter: `user_id=eq.${userId}`
        }, 
        callback
      )
      .subscribe();
  },

  subscribeToUsage(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('usage')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: TABLES.USAGE,
          filter: `user_id=eq.${userId}`
        }, 
        callback
      )
      .subscribe();
  },

  subscribeToSettings(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('settings')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: TABLES.SETTINGS,
          filter: `user_id=eq.${userId}`
        }, 
        callback
      )
      .subscribe();
  },
};

// Authentication helpers
export const auth = {
  async signUp(email: string, password: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
    });
    
    if (error) throw error;
    return data;
  },

  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) throw error;
    return data;
  },

  async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },

  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  },
};

export default supabase;
