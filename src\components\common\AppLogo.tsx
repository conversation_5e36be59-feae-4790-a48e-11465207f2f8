// App Logo Component with Lightning Effect

import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import Svg, { Path, Defs, LinearGradient, Stop, G } from 'react-native-svg';

interface AppLogoProps {
  size?: number;
  showText?: boolean;
  style?: ViewStyle;
  variant?: 'full' | 'icon' | 'text';
  animated?: boolean;
}

const AppLogo: React.FC<AppLogoProps> = ({
  size = 120,
  showText = true,
  style,
  variant = 'full',
  animated = false,
}) => {
  const iconSize = size * 0.6;
  const textSize = size * 0.12;

  // Lightning bolt path
  const lightningPath = `
    M${iconSize * 0.3},${iconSize * 0.1} 
    L${iconSize * 0.7},${iconSize * 0.4} 
    L${iconSize * 0.55},${iconSize * 0.4} 
    L${iconSize * 0.7},${iconSize * 0.9} 
    L${iconSize * 0.3},${iconSize * 0.6} 
    L${iconSize * 0.45},${iconSize * 0.6} 
    Z
  `;

  // Circle background path
  const circlePath = `
    M${iconSize * 0.5},${iconSize * 0.05}
    A${iconSize * 0.45},${iconSize * 0.45} 0 1,1 ${iconSize * 0.5},${iconSize * 0.95}
    A${iconSize * 0.45},${iconSize * 0.45} 0 1,1 ${iconSize * 0.5},${iconSize * 0.05}
  `;

  const renderIcon = () => (
    <View style={[styles.iconContainer, { width: iconSize, height: iconSize }]}>
      <Svg width={iconSize} height={iconSize} viewBox={`0 0 ${iconSize} ${iconSize}`}>
        <Defs>
          <LinearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#007AFF" />
            <Stop offset="50%" stopColor="#5AC8FA" />
            <Stop offset="100%" stopColor="#34C759" />
          </LinearGradient>
          <LinearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#FFCC02" />
            <Stop offset="50%" stopColor="#FF9500" />
            <Stop offset="100%" stopColor="#FFFFFF" />
          </LinearGradient>
          <LinearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#007AFF" stopOpacity="0.3" />
            <Stop offset="100%" stopColor="#34C759" stopOpacity="0.1" />
          </LinearGradient>
        </Defs>

        {/* Outer glow */}
        <G opacity={0.6}>
          <Path
            d={circlePath}
            fill="url(#glowGradient)"
            transform={`scale(1.1) translate(${-iconSize * 0.05}, ${-iconSize * 0.05})`}
          />
        </G>

        {/* Background circle */}
        <Path
          d={circlePath}
          fill="url(#backgroundGradient)"
          stroke="#FFFFFF"
          strokeWidth={2}
        />

        {/* Lightning bolt */}
        <Path
          d={lightningPath}
          fill="url(#lightningGradient)"
          stroke="#FFFFFF"
          strokeWidth={1.5}
          strokeLinejoin="round"
        />

        {/* Inner highlight */}
        <Path
          d={lightningPath}
          fill="none"
          stroke="#FFFFFF"
          strokeWidth={0.5}
          opacity={0.8}
        />
      </Svg>
    </View>
  );

  const renderText = () => (
    <View style={styles.textContainer}>
      <Text style={[styles.appName, { fontSize: textSize }]}>
        PREPAID USER
      </Text>
      <Text style={[styles.appSubtitle, { fontSize: textSize * 0.7 }]}>
        ELECTRICITY
      </Text>
    </View>
  );

  if (variant === 'icon') {
    return (
      <View style={[styles.container, style]}>
        {renderIcon()}
      </View>
    );
  }

  if (variant === 'text') {
    return (
      <View style={[styles.container, style]}>
        {renderText()}
      </View>
    );
  }

  return (
    <View style={[styles.container, { width: size, height: size }, style]}>
      {renderIcon()}
      {showText && renderText()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  textContainer: {
    alignItems: 'center',
  },
  appName: {
    fontWeight: 'bold',
    color: '#007AFF',
    textAlign: 'center',
    letterSpacing: 1,
  },
  appSubtitle: {
    fontWeight: '600',
    color: '#34C759',
    textAlign: 'center',
    letterSpacing: 0.5,
    marginTop: 2,
  },
});

export default AppLogo;
