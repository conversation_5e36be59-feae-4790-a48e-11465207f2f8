import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  console.log('🚀 Prepaid Electricity App starting...');

  const handlePress = (action: string) => {
    Alert.alert('Action', `You pressed: ${action}`);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>⚡ PREPAID USER - ELECTRICITY</Text>
        <Text style={styles.subtitle}>Development Version</Text>
      </View>

      {/* Content */}
      <View style={styles.content}>
        <View style={styles.dialContainer}>
          <View style={styles.dial}>
            <Text style={styles.dialValue}>75</Text>
            <Text style={styles.dialLabel}>Units</Text>
          </View>
        </View>

        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handlePress('Add Purchase')}
          >
            <Text style={styles.actionButtonText}>💰 Add Purchase</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handlePress('Record Usage')}
          >
            <Text style={styles.actionButtonText}>📝 Record Usage</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handlePress('View History')}
          >
            <Text style={styles.actionButtonText}>📊 View History</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.statusContainer}>
          <Text style={styles.statusTitle}>App Status</Text>
          <Text style={styles.statusText}>✅ React Native: Working</Text>
          <Text style={styles.statusText}>✅ TypeScript: Configured</Text>
          <Text style={styles.statusText}>✅ Expo: Running</Text>
          <Text style={styles.statusText}>✅ Components: Functional</Text>
          <Text style={styles.statusText}>🔄 Full App: In Development</Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  header: {
    backgroundColor: '#007AFF',
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
    marginTop: 4,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  dialContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  dial: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#FFFFFF',
    borderWidth: 8,
    borderColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  dialValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  dialLabel: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 4,
  },
  actionsContainer: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  actionButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  statusContainer: {
    backgroundColor: '#E3F2FD',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 12,
  },
  statusText: {
    fontSize: 14,
    color: '#000000',
    marginBottom: 4,
  },
});
