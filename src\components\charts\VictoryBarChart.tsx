// Victory Native Bar Chart Component
import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Victory<PERSON><PERSON>, VictoryBar, VictoryAxis, VictoryTheme, VictoryLabel } from 'victory-native';

interface DataPoint {
  x: number | string;
  y: number;
  label?: string;
}

interface VictoryBarChartProps {
  data: DataPoint[];
  width?: number;
  height?: number;
  title?: string;
  color?: string;
  showValues?: boolean;
  animate?: boolean;
  theme?: any;
  horizontal?: boolean;
}

const VictoryBarChart: React.FC<VictoryBarChartProps> = ({
  data,
  width = Dimensions.get('window').width - 40,
  height = 250,
  title,
  color = '#007AFF',
  showValues = false,
  animate = true,
  theme = VictoryTheme.material,
  horizontal = false,
}) => {
  if (data.length === 0) {
    return (
      <View style={[styles.container, { width, height }]}>
        {title && <Text style={styles.title}>{title}</Text>}
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No data available</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { width, height }]}>
      {title && <Text style={styles.title}>{title}</Text>}
      
      <VictoryChart
        theme={theme}
        width={width}
        height={height - 60} // Reserve space for title
        padding={{ left: 60, top: 20, right: 40, bottom: 60 }}
        domainPadding={{ x: 20 }}
        animate={animate ? { duration: 1000, onLoad: { duration: 500 } } : false}
      >
        <VictoryAxis
          dependentAxis={!horizontal}
          tickFormat={(t) => `${t}`}
          style={{
            tickLabels: { fontSize: 12, fill: '#8E8E93' },
            grid: { stroke: '#E5E5EA', strokeWidth: 1 },
            axis: { stroke: '#C6C6C8', strokeWidth: 1 },
          }}
        />
        <VictoryAxis
          dependentAxis={horizontal}
          tickFormat={(t) => `${t}`}
          style={{
            tickLabels: { fontSize: 12, fill: '#8E8E93' },
            grid: { stroke: '#E5E5EA', strokeWidth: 1 },
            axis: { stroke: '#C6C6C8', strokeWidth: 1 },
          }}
        />
        
        <VictoryBar
          data={data}
          horizontal={horizontal}
          style={{
            data: { 
              fill: color,
              fillOpacity: 0.8,
              stroke: color,
              strokeWidth: 1,
            },
          }}
          animate={animate ? { duration: 1000 } : false}
          labelComponent={showValues ? <VictoryLabel style={{ fill: '#000', fontSize: 12 }} /> : undefined}
        />
      </VictoryChart>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
  },
});

export default VictoryBarChart;
