// Appearance Settings Screen - Themes, Fonts, Colors with Live Previews

import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { useAppDispatch, useAppSelector } from '@/store';
import { selectTheme, setTheme } from '@/store/slices/settingsSlice';
import Container from '@/components/common/Container';
import QuickActionButton from '@/components/common/QuickActionButton';
import GradientDial from '@/components/common/GradientDial';
import { THEMES, FONTS } from '@/constants';

const AppearanceSettingsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const currentTheme = useAppSelector(selectTheme);
  
  const [selectedTheme, setSelectedTheme] = useState(currentTheme?.id || 'electric-blue');
  const [selectedFont, setSelectedFont] = useState('default');
  const [previewMode, setPreviewMode] = useState(false);

  const handleThemeChange = (themeId: string) => {
    setSelectedTheme(themeId);
    if (previewMode) {
      dispatch(setTheme(themeId));
    }
  };

  const handleSaveSettings = () => {
    dispatch(setTheme(selectedTheme));
    Alert.alert('Success', 'Appearance settings saved successfully!');
  };

  const togglePreviewMode = () => {
    setPreviewMode(!previewMode);
    if (!previewMode) {
      dispatch(setTheme(selectedTheme));
    } else {
      dispatch(setTheme(currentTheme?.id || 'electric-blue'));
    }
  };

  const selectedThemeData = THEMES.find(t => t.id === selectedTheme) || THEMES[0];

  const renderThemeOptions = () => (
    <View style={styles.themesContainer}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Themes</Text>
        <TouchableOpacity onPress={togglePreviewMode} style={styles.previewButton}>
          <Text style={styles.previewButtonText}>
            {previewMode ? 'Stop Preview' : 'Live Preview'}
          </Text>
        </TouchableOpacity>
      </View>

      {THEMES.map((theme) => (
        <TouchableOpacity
          key={theme.id}
          style={[
            styles.themeOption,
            selectedTheme === theme.id && styles.selectedTheme,
            { borderColor: theme.colors.primary }
          ]}
          onPress={() => handleThemeChange(theme.id)}
        >
          <View style={styles.themePreviewContainer}>
            <View style={[styles.themePreview, { backgroundColor: theme.colors.primary }]} />
            <View style={[styles.themePreview, { backgroundColor: theme.colors.secondary }]} />
            <View style={[styles.themePreview, { backgroundColor: theme.colors.accent }]} />
          </View>
          
          <View style={styles.themeInfo}>
            <Text style={[styles.themeName, { color: theme.colors.text }]}>
              {theme.name}
            </Text>
            <Text style={[styles.themeDescription, { color: theme.colors.textSecondary }]}>
              Primary: {theme.colors.primary}
            </Text>
          </View>
          
          {selectedTheme === theme.id && (
            <Text style={[styles.selectedIndicator, { color: theme.colors.primary }]}>
              ✓
            </Text>
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderFontOptions = () => (
    <View style={styles.fontsContainer}>
      <Text style={styles.sectionTitle}>Font Family</Text>
      {FONTS.map((font) => (
        <QuickActionButton
          key={font.value}
          title={font.label}
          icon="type"
          onPress={() => setSelectedFont(font.value)}
          variant={selectedFont === font.value ? 'primary' : 'secondary'}
          style={[styles.fontOption, { fontFamily: font.value !== 'default' ? font.value : undefined }]}
        />
      ))}
    </View>
  );

  const renderLivePreview = () => (
    <View style={styles.previewContainer}>
      <Text style={styles.sectionTitle}>Live Preview</Text>
      
      <View style={[styles.previewCard, { backgroundColor: selectedThemeData.colors.surface }]}>
        <Text style={[styles.previewTitle, { color: selectedThemeData.colors.text }]}>
          Sample Dashboard
        </Text>
        
        <GradientDial
          value={75}
          maxValue={100}
          size={120}
          strokeWidth={12}
          colors={selectedThemeData.gradients.primary}
          backgroundColor={selectedThemeData.colors.background}
          showValue={true}
          valueFormatter={(value) => `${value} Units`}
          animated={false}
          title="Current Units"
          showLightning={true}
        />
        
        <View style={styles.previewButtons}>
          <View style={[styles.previewButton, { backgroundColor: selectedThemeData.colors.primary }]}>
            <Text style={[styles.previewButtonText, { color: '#FFFFFF' }]}>
              Primary Button
            </Text>
          </View>
          
          <View style={[styles.previewButton, { 
            backgroundColor: 'transparent',
            borderWidth: 1,
            borderColor: selectedThemeData.colors.primary 
          }]}>
            <Text style={[styles.previewButtonText, { color: selectedThemeData.colors.primary }]}>
              Secondary Button
            </Text>
          </View>
        </View>
      </View>
    </View>
  );

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Appearance Settings</Text>

        {renderThemeOptions()}
        {renderFontOptions()}
        {renderLivePreview()}

        {/* Save Button */}
        <QuickActionButton
          title="Save Appearance Settings"
          icon="check"
          onPress={handleSaveSettings}
          variant="primary"
          gradient={true}
          colors={selectedThemeData.gradients.primary}
          style={styles.saveButton}
        />
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 24,
    textAlign: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
  },
  previewButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  previewButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  themesContainer: {
    marginBottom: 32,
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#F2F2F7',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  selectedTheme: {
    borderWidth: 3,
    shadowOpacity: 0.2,
  },
  themePreviewContainer: {
    flexDirection: 'row',
    marginRight: 12,
  },
  themePreview: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 4,
  },
  themeInfo: {
    flex: 1,
  },
  themeName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  themeDescription: {
    fontSize: 12,
  },
  selectedIndicator: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  fontsContainer: {
    marginBottom: 32,
  },
  fontOption: {
    marginBottom: 8,
  },
  previewContainer: {
    marginBottom: 32,
  },
  previewCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  previewButtons: {
    flexDirection: 'row',
    marginTop: 16,
    gap: 12,
  },
  saveButton: {
    marginTop: 20,
    marginBottom: 40,
  },
});

export default AppearanceSettingsScreen;
