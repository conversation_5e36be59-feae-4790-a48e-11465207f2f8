{"name": "prepaid-electricity-app", "version": "1.0.0", "description": "A modern electricity prepaid meter tracking mobile application", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android --profile preview", "build:apk": "eas build --platform android --profile apk", "build:production": "eas build --platform android --profile production", "build:ios": "eas build --platform ios", "test": "jest", "test:e2e": "detox test", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "bundle": "expo export"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/drawer": "^7.4.2", "@react-navigation/native": "^7.1.11", "@react-navigation/native-stack": "^7.3.16", "@reduxjs/toolkit": "^2.8.2", "@supabase/supabase-js": "^2.50.0", "@tamagui/animations-react-native": "^1.126.18", "@tamagui/config": "^1.126.18", "@tamagui/core": "^1.126.18", "date-fns": "^4.1.0", "expo": "~53.0.11", "expo-linear-gradient": "^14.1.5", "expo-notifications": "^0.31.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "victory-native": "^41.17.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.56.0", "eslint-config-expo": "~9.2.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "prettier": "^3.1.1", "typescript": "~5.8.3"}, "keywords": ["react-native", "expo", "electricity", "prepaid", "meter", "tracking", "mobile"], "author": "Pathfinders2c", "license": "MIT", "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["@tamagui/core", "react-native-vector-icons", "redux-persist"], "listUnknownPackages": false}}}}