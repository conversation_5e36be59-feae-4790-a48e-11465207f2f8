// Supabase Sync Hook - Real-time data synchronization
import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { 
  setPurchases, 
  addPurchase as addPurchaseAction,
  updatePurchase as updatePurchaseAction,
  deletePurchase as deletePurchaseAction,
} from '@/store/slices/purchasesSlice';
import {
  setUsage,
  addUsageRecord as addUsageAction,
  updateUsageRecord as updateUsageAction,
  deleteUsageRecord as deleteUsageAction,
} from '@/store/slices/usageSlice';
import { setUser } from '@/store/slices/userSlice';
import { 
  purchaseService, 
  usageService, 
  userService, 
  subscriptions,
  auth 
} from '@/services/supabase';
import { Purchase, UsageRecord } from '@/types';

export const useSupabaseSync = () => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(state => state.user);

  // Sync purchases from Supabase
  const syncPurchases = useCallback(async (userId: string) => {
    try {
      const purchases = await purchaseService.getPurchases(userId);
      dispatch(setPurchases(purchases));
    } catch (error) {
      console.error('Failed to sync purchases:', error);
    }
  }, [dispatch]);

  // Sync usage records from Supabase
  const syncUsage = useCallback(async (userId: string) => {
    try {
      const usage = await usageService.getUsageRecords(userId);
      dispatch(setUsage(usage));
    } catch (error) {
      console.error('Failed to sync usage:', error);
    }
  }, [dispatch]);

  // Sync user data from Supabase
  const syncUser = useCallback(async (userId: string) => {
    try {
      const userData = await userService.getUser(userId);
      dispatch(setUser(userData));
    } catch (error) {
      console.error('Failed to sync user data:', error);
    }
  }, [dispatch]);

  // Add purchase to Supabase and update local state
  const addPurchase = useCallback(async (purchase: Omit<Purchase, 'id' | 'createdAt'>) => {
    try {
      const newPurchase = await purchaseService.addPurchase(purchase);
      dispatch(addPurchaseAction(newPurchase));
      return newPurchase;
    } catch (error) {
      console.error('Failed to add purchase:', error);
      throw error;
    }
  }, [dispatch]);

  // Update purchase in Supabase and local state
  const updatePurchase = useCallback(async (purchaseId: string, updates: Partial<Purchase>) => {
    try {
      const updatedPurchase = await purchaseService.updatePurchase(purchaseId, updates);
      dispatch(updatePurchaseAction(updatedPurchase));
      return updatedPurchase;
    } catch (error) {
      console.error('Failed to update purchase:', error);
      throw error;
    }
  }, [dispatch]);

  // Delete purchase from Supabase and local state
  const deletePurchase = useCallback(async (purchaseId: string) => {
    try {
      await purchaseService.deletePurchase(purchaseId);
      dispatch(deletePurchaseAction(purchaseId));
    } catch (error) {
      console.error('Failed to delete purchase:', error);
      throw error;
    }
  }, [dispatch]);

  // Add usage record to Supabase and update local state
  const addUsageRecord = useCallback(async (usage: Omit<UsageRecord, 'id' | 'createdAt'>) => {
    try {
      const newUsage = await usageService.addUsageRecord(usage);
      dispatch(addUsageAction(newUsage));
      return newUsage;
    } catch (error) {
      console.error('Failed to add usage record:', error);
      throw error;
    }
  }, [dispatch]);

  // Update usage record in Supabase and local state
  const updateUsageRecord = useCallback(async (usageId: string, updates: Partial<UsageRecord>) => {
    try {
      const updatedUsage = await usageService.updateUsageRecord(usageId, updates);
      dispatch(updateUsageAction(updatedUsage));
      return updatedUsage;
    } catch (error) {
      console.error('Failed to update usage record:', error);
      throw error;
    }
  }, [dispatch]);

  // Delete usage record from Supabase and local state
  const deleteUsageRecord = useCallback(async (usageId: string) => {
    try {
      await usageService.deleteUsageRecord(usageId);
      dispatch(deleteUsageAction(usageId));
    } catch (error) {
      console.error('Failed to delete usage record:', error);
      throw error;
    }
  }, [dispatch]);

  // Initialize real-time subscriptions
  useEffect(() => {
    let purchaseSubscription: any;
    let usageSubscription: any;

    const initializeSubscriptions = async () => {
      const user = await auth.getCurrentUser();
      if (!user) return;

      // Subscribe to purchases changes
      purchaseSubscription = subscriptions.subscribeToPurchases(
        user.id,
        (payload) => {
          console.log('Purchase change:', payload);
          const { eventType, new: newRecord, old: oldRecord } = payload;
          
          switch (eventType) {
            case 'INSERT':
              dispatch(addPurchaseAction(newRecord));
              break;
            case 'UPDATE':
              dispatch(updatePurchaseAction(newRecord));
              break;
            case 'DELETE':
              dispatch(deletePurchaseAction(oldRecord.id));
              break;
          }
        }
      );

      // Subscribe to usage changes
      usageSubscription = subscriptions.subscribeToUsage(
        user.id,
        (payload) => {
          console.log('Usage change:', payload);
          const { eventType, new: newRecord, old: oldRecord } = payload;
          
          switch (eventType) {
            case 'INSERT':
              dispatch(addUsageAction(newRecord));
              break;
            case 'UPDATE':
              dispatch(updateUsageAction(newRecord));
              break;
            case 'DELETE':
              dispatch(deleteUsageAction(oldRecord.id));
              break;
          }
        }
      );

      // Initial sync
      await Promise.all([
        syncPurchases(user.id),
        syncUsage(user.id),
        syncUser(user.id),
      ]);
    };

    initializeSubscriptions();

    // Cleanup subscriptions
    return () => {
      if (purchaseSubscription) {
        purchaseSubscription.unsubscribe();
      }
      if (usageSubscription) {
        usageSubscription.unsubscribe();
      }
    };
  }, [dispatch, syncPurchases, syncUsage, syncUser]);

  return {
    // Sync functions
    syncPurchases,
    syncUsage,
    syncUser,
    
    // CRUD operations
    addPurchase,
    updatePurchase,
    deletePurchase,
    addUsageRecord,
    updateUsageRecord,
    deleteUsageRecord,
  };
};

export default useSupabaseSync;
