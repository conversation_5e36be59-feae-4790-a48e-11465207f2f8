// Initial Setup Screen for First-Time Users

import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet, Alert, TextInput } from 'react-native';
import { useAppDispatch } from '@/store';
import { initializeUser } from '@/store/slices/userSlice';
import Container from '@/components/common/Container';
import QuickActionButton from '@/components/common/QuickActionButton';
import AppLogo from '@/components/common/AppLogo';
import { CURRENCIES, UNIT_TYPES, THEMES } from '@/constants';

interface InitialSetupScreenProps {
  onComplete: () => void;
}

const InitialSetupScreen: React.FC<InitialSetupScreenProps> = ({ onComplete }) => {
  const dispatch = useAppDispatch();

  const [currentStep, setCurrentStep] = useState(1);
  const [setupData, setSetupData] = useState({
    initialUnits: '',
    currency: 'USD',
    customCurrency: '',
    unitType: 'Units',
    customUnit: '',
    costPerUnit: '0.15',
    thresholdLimit: '10',
    theme: 'electric-blue',
    notificationsEnabled: true,
    notificationTime: '18:00',
  });

  const totalSteps = 4;

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    try {
      // Validate required fields
      if (!setupData.initialUnits || parseFloat(setupData.initialUnits) < 0) {
        Alert.alert('Error', 'Please enter a valid initial unit value');
        return;
      }

      if (!setupData.costPerUnit || parseFloat(setupData.costPerUnit) <= 0) {
        Alert.alert('Error', 'Please enter a valid cost per unit');
        return;
      }

      if (!setupData.thresholdLimit || parseFloat(setupData.thresholdLimit) <= 0) {
        Alert.alert('Error', 'Please enter a valid threshold limit');
        return;
      }

      // Initialize user with setup data
      await dispatch(initializeUser({
        unitType: setupData.unitType,
        customUnitName: setupData.unitType === 'Custom' ? setupData.customUnit : undefined,
        currency: setupData.currency,
        customCurrencyName: setupData.currency === 'Custom' ? setupData.customCurrency : undefined,
        costPerUnit: parseFloat(setupData.costPerUnit),
        thresholdLimit: parseFloat(setupData.thresholdLimit),
        theme: setupData.theme,
        notificationsEnabled: setupData.notificationsEnabled,
        notificationTime: setupData.notificationTime,
        initialUnits: parseFloat(setupData.initialUnits),
      })).unwrap();

      onComplete();
    } catch (error) {
      Alert.alert('Error', 'Failed to complete setup. Please try again.');
    }
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Welcome to Prepaid Electricity!</Text>
      <Text style={styles.stepDescription}>
        Let's set up your app with your current electricity meter reading and preferences.
      </Text>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Current Meter Reading</Text>
        <TextInput
          style={styles.textInput}
          value={setupData.initialUnits}
          onChangeText={(value) => setSetupData({ ...setupData, initialUnits: value })}
          placeholder="Enter your current units"
          keyboardType="numeric"
        />
        <Text style={styles.inputHelper}>
          Check your electricity meter and enter the current reading
        </Text>
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Currency & Units</Text>
      <Text style={styles.stepDescription}>
        Choose your preferred currency and unit type for tracking electricity.
      </Text>

      <View style={styles.optionsSection}>
        <Text style={styles.optionTitle}>Currency</Text>
        {CURRENCIES.map((currency) => (
          <QuickActionButton
            key={currency.value}
            title={`${currency.label} (${currency.symbol})`}
            icon="dollar"
            onPress={() => setSetupData({ ...setupData, currency: currency.value })}
            variant={setupData.currency === currency.value ? 'primary' : 'secondary'}
            style={styles.optionButton}
          />
        ))}
        
        <QuickActionButton
          title="Custom Currency"
          icon="edit"
          onPress={() => setSetupData({ ...setupData, currency: 'Custom' })}
          variant={setupData.currency === 'Custom' ? 'primary' : 'secondary'}
          style={styles.optionButton}
        />
        
        {setupData.currency === 'Custom' && (
          <TextInput
            style={styles.textInput}
            value={setupData.customCurrency}
            onChangeText={(value) => setSetupData({ ...setupData, customCurrency: value })}
            placeholder="Enter currency name"
          />
        )}
      </View>

      <View style={styles.optionsSection}>
        <Text style={styles.optionTitle}>Unit Type</Text>
        {UNIT_TYPES.map((unit) => (
          <QuickActionButton
            key={unit.value}
            title={unit.label}
            icon="zap"
            onPress={() => setSetupData({ ...setupData, unitType: unit.value })}
            variant={setupData.unitType === unit.value ? 'primary' : 'secondary'}
            style={styles.optionButton}
          />
        ))}
        
        {setupData.unitType === 'Custom' && (
          <TextInput
            style={styles.textInput}
            value={setupData.customUnit}
            onChangeText={(value) => setSetupData({ ...setupData, customUnit: value })}
            placeholder="Enter unit name"
          />
        )}
      </View>
    </View>
  );

  const renderStep3 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Cost & Threshold Settings</Text>
      <Text style={styles.stepDescription}>
        Set your electricity cost per unit and low units warning threshold.
      </Text>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Cost Per Unit</Text>
        <TextInput
          style={styles.textInput}
          value={setupData.costPerUnit}
          onChangeText={(value) => setSetupData({ ...setupData, costPerUnit: value })}
          placeholder="0.15"
          keyboardType="decimal-pad"
        />
        <Text style={styles.inputHelper}>
          Enter the cost per unit of electricity in your currency
        </Text>
      </View>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Low Units Threshold</Text>
        <TextInput
          style={styles.textInput}
          value={setupData.thresholdLimit}
          onChangeText={(value) => setSetupData({ ...setupData, thresholdLimit: value })}
          placeholder="10"
          keyboardType="numeric"
        />
        <Text style={styles.inputHelper}>
          You'll get a warning when units drop below this level
        </Text>
      </View>
    </View>
  );

  const renderStep4 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Personalization</Text>
      <Text style={styles.stepDescription}>
        Choose your app theme and notification preferences.
      </Text>

      <View style={styles.optionsSection}>
        <Text style={styles.optionTitle}>Theme</Text>
        <View style={styles.themeGrid}>
          {THEMES.slice(0, 3).map((theme) => (
            <QuickActionButton
              key={theme.id}
              title={theme.name}
              icon="palette"
              onPress={() => setSetupData({ ...setupData, theme: theme.id })}
              variant={setupData.theme === theme.id ? 'primary' : 'secondary'}
              style={styles.themeButton}
            />
          ))}
        </View>
      </View>

      <View style={styles.inputSection}>
        <Text style={styles.inputLabel}>Daily Reminder Time</Text>
        <TextInput
          style={styles.textInput}
          value={setupData.notificationTime}
          onChangeText={(value) => setSetupData({ ...setupData, notificationTime: value })}
          placeholder="18:00"
        />
        <Text style={styles.inputHelper}>
          Time for daily usage recording reminders (24-hour format)
        </Text>
      </View>
    </View>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return renderStep1();
      case 2:
        return renderStep2();
      case 3:
        return renderStep3();
      case 4:
        return renderStep4();
      default:
        return renderStep1();
    }
  };

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        {/* Header */}
        <View style={styles.header}>
          <AppLogo size={100} showText={true} variant="full" />
        </View>

        {/* Progress Indicator */}
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>Step {currentStep} of {totalSteps}</Text>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, { width: `${(currentStep / totalSteps) * 100}%` }]} />
          </View>
        </View>

        {/* Current Step Content */}
        {renderCurrentStep()}

        {/* Navigation Buttons */}
        <View style={styles.navigationContainer}>
          {currentStep > 1 && (
            <QuickActionButton
              title="Back"
              icon="arrow-left"
              onPress={handleBack}
              variant="secondary"
              style={styles.navButton}
            />
          )}
          
          <QuickActionButton
            title={currentStep === totalSteps ? 'Complete Setup' : 'Next'}
            icon={currentStep === totalSteps ? 'check' : 'arrow-right'}
            onPress={handleNext}
            variant="primary"
            gradient={true}
            style={styles.navButton}
          />
        </View>
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  progressContainer: {
    marginBottom: 32,
  },
  progressText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#F2F2F7',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: 2,
  },
  stepContainer: {
    marginBottom: 32,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 12,
  },
  stepDescription: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  inputSection: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
  },
  inputHelper: {
    fontSize: 12,
    color: '#8E8E93',
    marginTop: 4,
  },
  optionsSection: {
    marginBottom: 24,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  optionButton: {
    marginBottom: 8,
  },
  themeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  themeButton: {
    flex: 1,
    minWidth: '30%',
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    marginTop: 20,
  },
  navButton: {
    flex: 1,
  },
});

export default InitialSetupScreen;
