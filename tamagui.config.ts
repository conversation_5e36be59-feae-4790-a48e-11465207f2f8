import { config } from '@tamagui/config/v3'
import { createTamagui } from '@tamagui/core'

const appConfig = createTamagui({
  ...config,
  themes: {
    ...config.themes,
    electric_blue: {
      background: '#F2F2F7',
      backgroundHover: '#E5E5EA',
      backgroundPress: '#D1D1D6',
      backgroundFocus: '#E5E5EA',
      color: '#000000',
      colorHover: '#000000',
      colorPress: '#000000',
      colorFocus: '#000000',
      borderColor: '#C6C6C8',
      borderColorHover: '#AEAEB2',
      borderColorPress: '#8E8E93',
      borderColorFocus: '#007AFF',
      placeholderColor: '#8E8E93',
      primary: '#007AFF',
      primaryHover: '#0056CC',
      primaryPress: '#004499',
      primaryFocus: '#007AFF',
      secondary: '#5AC8FA',
      secondaryHover: '#32ADF0',
      secondaryPress: '#0A84FF',
      secondaryFocus: '#5AC8FA',
      accent: '#FF9500',
      accentHover: '#FF8C00',
      accentPress: '#E6780E',
      accentFocus: '#FF9500',
      success: '#34C759',
      successHover: '#28A745',
      successPress: '#1E7E34',
      successFocus: '#34C759',
      warning: '#FF9500',
      warningHover: '#FF8C00',
      warningPress: '#E6780E',
      warningFocus: '#FF9500',
      error: '#FF3B30',
      errorHover: '#FF1D0C',
      errorPress: '#D70015',
      errorFocus: '#FF3B30',
      surface: '#FFFFFF',
      surfaceHover: '#F2F2F7',
      surfacePress: '#E5E5EA',
      surfaceFocus: '#F2F2F7',
    },
    energy_green: {
      background: '#F2F2F7',
      backgroundHover: '#E5E5EA',
      backgroundPress: '#D1D1D6',
      backgroundFocus: '#E5E5EA',
      color: '#000000',
      colorHover: '#000000',
      colorPress: '#000000',
      colorFocus: '#000000',
      borderColor: '#C6C6C8',
      borderColorHover: '#AEAEB2',
      borderColorPress: '#8E8E93',
      borderColorFocus: '#34C759',
      placeholderColor: '#8E8E93',
      primary: '#34C759',
      primaryHover: '#28A745',
      primaryPress: '#1E7E34',
      primaryFocus: '#34C759',
      secondary: '#30D158',
      secondaryHover: '#28A745',
      secondaryPress: '#1E7E34',
      secondaryFocus: '#30D158',
      accent: '#FF9500',
      accentHover: '#FF8C00',
      accentPress: '#E6780E',
      accentFocus: '#FF9500',
      success: '#34C759',
      successHover: '#28A745',
      successPress: '#1E7E34',
      successFocus: '#34C759',
      warning: '#FF9500',
      warningHover: '#FF8C00',
      warningPress: '#E6780E',
      warningFocus: '#FF9500',
      error: '#FF3B30',
      errorHover: '#FF1D0C',
      errorPress: '#D70015',
      errorFocus: '#FF3B30',
      surface: '#FFFFFF',
      surfaceHover: '#F2F2F7',
      surfacePress: '#E5E5EA',
      surfaceFocus: '#F2F2F7',
    },
    power_purple: {
      background: '#F2F2F7',
      backgroundHover: '#E5E5EA',
      backgroundPress: '#D1D1D6',
      backgroundFocus: '#E5E5EA',
      color: '#000000',
      colorHover: '#000000',
      colorPress: '#000000',
      colorFocus: '#000000',
      borderColor: '#C6C6C8',
      borderColorHover: '#AEAEB2',
      borderColorPress: '#8E8E93',
      borderColorFocus: '#AF52DE',
      placeholderColor: '#8E8E93',
      primary: '#AF52DE',
      primaryHover: '#9932CC',
      primaryPress: '#8B008B',
      primaryFocus: '#AF52DE',
      secondary: '#BF5AF2',
      secondaryHover: '#9932CC',
      secondaryPress: '#8B008B',
      secondaryFocus: '#BF5AF2',
      accent: '#FF9500',
      accentHover: '#FF8C00',
      accentPress: '#E6780E',
      accentFocus: '#FF9500',
      success: '#34C759',
      successHover: '#28A745',
      successPress: '#1E7E34',
      successFocus: '#34C759',
      warning: '#FF9500',
      warningHover: '#FF8C00',
      warningPress: '#E6780E',
      warningFocus: '#FF9500',
      error: '#FF3B30',
      errorHover: '#FF1D0C',
      errorPress: '#D70015',
      errorFocus: '#FF3B30',
      surface: '#FFFFFF',
      surfaceHover: '#F2F2F7',
      surfacePress: '#E5E5EA',
      surfaceFocus: '#F2F2F7',
    },
    solar_orange: {
      background: '#F2F2F7',
      backgroundHover: '#E5E5EA',
      backgroundPress: '#D1D1D6',
      backgroundFocus: '#E5E5EA',
      color: '#000000',
      colorHover: '#000000',
      colorPress: '#000000',
      colorFocus: '#000000',
      borderColor: '#C6C6C8',
      borderColorHover: '#AEAEB2',
      borderColorPress: '#8E8E93',
      borderColorFocus: '#FF9500',
      placeholderColor: '#8E8E93',
      primary: '#FF9500',
      primaryHover: '#FF8C00',
      primaryPress: '#E6780E',
      primaryFocus: '#FF9500',
      secondary: '#FFCC02',
      secondaryHover: '#FFB000',
      secondaryPress: '#E6A000',
      secondaryFocus: '#FFCC02',
      accent: '#007AFF',
      accentHover: '#0056CC',
      accentPress: '#004499',
      accentFocus: '#007AFF',
      success: '#34C759',
      successHover: '#28A745',
      successPress: '#1E7E34',
      successFocus: '#34C759',
      warning: '#FF9500',
      warningHover: '#FF8C00',
      warningPress: '#E6780E',
      warningFocus: '#FF9500',
      error: '#FF3B30',
      errorHover: '#FF1D0C',
      errorPress: '#D70015',
      errorFocus: '#FF3B30',
      surface: '#FFFFFF',
      surfaceHover: '#F2F2F7',
      surfacePress: '#E5E5EA',
      surfaceFocus: '#F2F2F7',
    },
    dark: {
      background: '#000000',
      backgroundHover: '#1C1C1E',
      backgroundPress: '#2C2C2E',
      backgroundFocus: '#1C1C1E',
      color: '#FFFFFF',
      colorHover: '#FFFFFF',
      colorPress: '#FFFFFF',
      colorFocus: '#FFFFFF',
      borderColor: '#38383A',
      borderColorHover: '#48484A',
      borderColorPress: '#58585A',
      borderColorFocus: '#0A84FF',
      placeholderColor: '#8E8E93',
      primary: '#0A84FF',
      primaryHover: '#0056CC',
      primaryPress: '#004499',
      primaryFocus: '#0A84FF',
      secondary: '#64D2FF',
      secondaryHover: '#32ADF0',
      secondaryPress: '#0A84FF',
      secondaryFocus: '#64D2FF',
      accent: '#FF9F0A',
      accentHover: '#FF8C00',
      accentPress: '#E6780E',
      accentFocus: '#FF9F0A',
      success: '#32D74B',
      successHover: '#28A745',
      successPress: '#1E7E34',
      successFocus: '#32D74B',
      warning: '#FF9F0A',
      warningHover: '#FF8C00',
      warningPress: '#E6780E',
      warningFocus: '#FF9F0A',
      error: '#FF453A',
      errorHover: '#FF1D0C',
      errorPress: '#D70015',
      errorFocus: '#FF453A',
      surface: '#1C1C1E',
      surfaceHover: '#2C2C2E',
      surfacePress: '#3A3A3C',
      surfaceFocus: '#2C2C2E',
    },
  },
})

export type AppConfig = typeof appConfig

declare module '@tamagui/core' {
  interface TamaguiCustomConfig extends AppConfig {}
}

export default appConfig
