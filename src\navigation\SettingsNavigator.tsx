// Settings Navigator

import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import SettingsScreen from '@/screens/Settings/SettingsScreen';
import GeneralSettingsScreen from '@/screens/Settings/GeneralSettingsScreen';
import AppearanceSettingsScreen from '@/screens/Settings/AppearanceSettingsScreen';
import ResetOptionsScreen from '@/screens/Settings/ResetOptionsScreen';
import { SettingsStackParamList } from '@/types/navigation';

const Stack = createNativeStackNavigator<SettingsStackParamList>();

const SettingsNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="SettingsMain"
      screenOptions={{
        headerStyle: {
          backgroundColor: '#007AFF',
        },
        headerTintColor: '#FFFFFF',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      <Stack.Screen
        name="SettingsMain"
        component={SettingsScreen}
        options={{
          title: 'Settings',
        }}
      />
      <Stack.Screen
        name="GeneralSettings"
        component={GeneralSettingsScreen}
        options={{
          title: 'General Settings',
        }}
      />
      <Stack.Screen
        name="AppearanceSettings"
        component={AppearanceSettingsScreen}
        options={{
          title: 'Appearance',
        }}
      />
      <Stack.Screen
        name="ResetOptions"
        component={ResetOptionsScreen}
        options={{
          title: 'Reset Options',
        }}
      />
    </Stack.Navigator>
  );
};

export default SettingsNavigator;
