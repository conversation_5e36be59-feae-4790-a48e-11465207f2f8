{"expo": {"name": "PREPAID USER - ELECTRICITY", "slug": "prepaid-user-electricity", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "description": "A modern electricity prepaid meter tracking mobile application", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#007AFF"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.pathfinders2c.prepaidelectricity"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#007AFF"}, "edgeToEdgeEnabled": true, "package": "com.pathfinders2c.prepaidelectricity", "versionCode": 1, "permissions": ["NOTIFICATIONS", "VIBRATE", "WAKE_LOCK"]}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "prepaid-electricity-app"}}, "plugins": ["expo-notifications"]}}